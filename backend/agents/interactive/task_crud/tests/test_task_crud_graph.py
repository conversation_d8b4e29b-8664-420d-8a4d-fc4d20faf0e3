"""
Unit Tests for Task CRUD Graph

This module contains comprehensive unit tests for the Task CRUD StateGraph,
testing the complete workflow from routing through individual operations.
"""

import pytest
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any
from unittest.mock import patch, AsyncMock, MagicMock

from langgraph.graph import StateGraph, END
from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.task_crud.router import task_crud_router
from backend.agents.interactive.task_crud.nodes.create import create_task
from backend.agents.interactive.task_crud.nodes.read import read_task
from backend.agents.interactive.task_crud.nodes.update import update_task
from backend.agents.interactive.task_crud.nodes.delete import delete_task
from backend.agents.interactive.task_crud.nodes.date_parser import parse_date
from backend.agents.interactive.task_crud.tests.mocks.mock_shared import MockVoyageClient


class MockVoyage:
    """Mock Voyage client for testing."""
    
    def __init__(self):
        self.client = MockVoyageClient()


def create_task_crud_graph() -> StateGraph:
    """
    Create a Task CRUD StateGraph for testing.
    
    Returns:
        StateGraph: Compiled Task CRUD workflow graph
    """
    # Create the graph with Dict[str, Any] as the state type
    workflow = StateGraph(Dict[str, Any])

    # Add the router node for intent classification
    workflow.add_node("taskCrudRouter", task_crud_router)
    
    # Add the date parser node for preprocessing natural language dates
    workflow.add_node("parse_date", parse_date)
    
    # Add operation nodes
    workflow.add_node("create_task", create_task)
    workflow.add_node("read_task", read_task)
    workflow.add_node("update_task", update_task)
    workflow.add_node("delete_task", delete_task)

    # Set entry point
    workflow.set_entry_point("taskCrudRouter")

    # Add conditional edges from router to operation nodes
    workflow.add_conditional_edges(
        "taskCrudRouter",
        lambda state: state.get("next", "FINISH"),
        {
            "create_task": "create_task",
            "read_task": "read_task",
            "update_task": "update_task",
            "delete_task": "delete_task",
            "parse_date": "parse_date",
            "FINISH": END,
        }
    )

    # Add conditional edges from operation nodes
    for node in ["create_task", "read_task", "update_task", "delete_task", "parse_date"]:
        workflow.add_conditional_edges(
            node,
            lambda state: state.get("next", "FINISH"),
            {
                "create_task": "create_task",
                "read_task": "read_task",
                "update_task": "update_task",
                "delete_task": "delete_task",
                "parse_date": "parse_date",
                "taskCrudRouter": "taskCrudRouter",
                "FINISH": END,
            }
        )

    # Compile the workflow
    return workflow.compile()


@pytest.fixture
def task_crud_graph():
    """Fixture for Task CRUD StateGraph."""
    return create_task_crud_graph()


@pytest.fixture
def mock_voyage():
    """Fixture for mock Voyage client."""
    return MockVoyage()


@pytest.fixture
def sample_task_data(tenant_id, user_id):
    """Fixture for sample task data."""
    return {
        "title": "Review contract for Smith case",
        "description": "Review the employment contract for the Smith vs. Company case",
        "status": "todo",
        "priority": "high",
        "due_date": (datetime.now() + timedelta(days=3)).strftime("%Y-%m-%d"),
        "assigned_to": user_id,
        "related_case": str(uuid.uuid4()),
        "tenant_id": tenant_id,
        "created_by": user_id,
        "ai_metadata": {
            "created_from": "natural_language",
            "original_message": "Create a task to review the contract for Smith case",
            "extraction_timestamp": datetime.now().isoformat()
        }
    }


@pytest.mark.asyncio
class TestTaskCrudGraph:
    """Test suite for Task CRUD StateGraph."""

    async def test_create_task_happy_path(
        self, 
        task_crud_graph, 
        mock_voyage, 
        mock_task_database, 
        tenant_id, 
        user_id, 
        sample_task_data
    ):
        """Test successful task creation through the graph."""
        # Prepare initial state
        state = {
            "messages": [
                {"role": "user", "content": "Create a task to review the contract for Smith case due in 3 days"}
            ],
            "tenant_id": tenant_id,
            "user_id": user_id,
            "thread_id": str(uuid.uuid4()),
        }
        
        # Prepare config
        config = {
            "configurable": {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "thread_id": str(uuid.uuid4())
            }
        }
        
        # Mock the task creation
        created_task = {
            "id": str(uuid.uuid4()),
            **sample_task_data,
            "created_at": datetime.now().isoformat(),
            "updated_at": None
        }
        
        with patch("backend.agents.interactive.task_crud.nodes.create.get_tool_executor") as mock_get_executor:
            mock_executor = AsyncMock()
            mock_executor.execute_tool.return_value = created_task
            mock_get_executor.return_value = mock_executor
            
            # Execute the graph
            result = await task_crud_graph.ainvoke(state, config)
            
            # Verify the result
            assert "current_task" in result
            assert result["current_task"]["id"] == created_task["id"]
            assert result["current_task"]["title"] == sample_task_data["title"]
            assert result["current_task"]["tenant_id"] == tenant_id
            
            # Verify success message was added
            assert any(
                "created" in msg.get("content", "").lower() 
                for msg in result.get("messages", [])
                if msg.get("role") == "assistant"
            )
            
            # Verify tool was called with correct parameters
            mock_executor.execute_tool.assert_called_once()
            call_args = mock_executor.execute_tool.call_args
            assert call_args[1]["tool_name"] == "create_task"
            assert call_args[1]["tool_args"]["tenant_id"] == tenant_id

    async def test_read_task(
        self,
        task_crud_graph,
        mock_voyage,
        mock_task_database,
        tenant_id,
        user_id
    ):
        """Test task reading/listing through the graph."""
        # Prepare initial state
        state = {
            "messages": [
                {"role": "user", "content": "List all tasks"}
            ],
            "tenant_id": tenant_id,
            "user_id": user_id,
            "thread_id": str(uuid.uuid4()),
        }

        # Prepare config
        config = {
            "configurable": {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "thread_id": str(uuid.uuid4())
            }
        }

        # Mock task list
        task_list = [
            {
                "id": str(uuid.uuid4()),
                "tenant_id": tenant_id,
                "title": "Task 1",
                "description": "First task",
                "status": "todo",
                "priority": "medium",
                "created_by": user_id,
                "created_at": datetime.now().isoformat()
            },
            {
                "id": str(uuid.uuid4()),
                "tenant_id": tenant_id,
                "title": "Task 2",
                "description": "Second task",
                "status": "in_progress",
                "priority": "high",
                "created_by": user_id,
                "created_at": datetime.now().isoformat()
            }
        ]

        with patch("backend.agents.interactive.task_crud.nodes.read.get_tool_executor") as mock_get_executor:
            mock_executor = AsyncMock()
            mock_executor.execute_tool.return_value = task_list
            mock_get_executor.return_value = mock_executor

            # Execute the graph
            result = await task_crud_graph.ainvoke(state, config)

            # Verify the result
            assert "tasks" in result
            assert len(result["tasks"]) == 2
            assert all(task["tenant_id"] == tenant_id for task in result["tasks"])

            # Verify response message was added
            assert any(
                "found" in msg.get("content", "").lower() or "tasks" in msg.get("content", "").lower()
                for msg in result.get("messages", [])
                if msg.get("role") == "assistant"
            )

            # Verify tool was called with correct parameters
            mock_executor.execute_tool.assert_called_once()
            call_args = mock_executor.execute_tool.call_args
            assert call_args[1]["tool_name"] == "list_tasks"
            assert call_args[1]["tenant_id"] == tenant_id

    async def test_update_task_conflict(
        self,
        task_crud_graph,
        mock_voyage,
        mock_task_database,
        tenant_id,
        user_id
    ):
        """Test updating a nonexistent task (should return error)."""
        nonexistent_task_id = str(uuid.uuid4())

        # Prepare initial state
        state = {
            "messages": [
                {"role": "user", "content": f"Update task {nonexistent_task_id} status to done"}
            ],
            "tenant_id": tenant_id,
            "user_id": user_id,
            "thread_id": str(uuid.uuid4()),
        }

        # Prepare config
        config = {
            "configurable": {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "thread_id": str(uuid.uuid4())
            }
        }

        with patch("backend.agents.interactive.task_crud.nodes.update.get_tool_executor") as mock_get_executor:
            mock_executor = AsyncMock()
            # Simulate task not found - get_task returns None
            mock_executor.execute_tool.return_value = None
            mock_get_executor.return_value = mock_executor

            # Execute the graph
            result = await task_crud_graph.ainvoke(state, config)

            # Verify error handling
            assert "error" in result or any(
                "not found" in msg.get("content", "").lower() or
                "couldn't" in msg.get("content", "").lower() or
                "unable" in msg.get("content", "").lower()
                for msg in result.get("messages", [])
                if msg.get("role") == "assistant"
            )

            # Verify get_task was called (update_task shouldn't be called if task not found)
            mock_executor.execute_tool.assert_called_once()
            call_args = mock_executor.execute_tool.call_args
            assert call_args[1]["tool_name"] == "get_task"
            assert call_args[1]["tool_args"]["task_id"] == nonexistent_task_id
            assert call_args[1]["tenant_id"] == tenant_id

    async def test_delete_task(
        self,
        task_crud_graph,
        mock_voyage,
        mock_task_database,
        tenant_id,
        user_id
    ):
        """Test task deletion through the graph."""
        task_id = str(uuid.uuid4())

        # Prepare initial state
        state = {
            "messages": [
                {"role": "user", "content": f"Delete task {task_id} yes confirm"}
            ],
            "tenant_id": tenant_id,
            "user_id": user_id,
            "thread_id": str(uuid.uuid4()),
        }

        # Prepare config
        config = {
            "configurable": {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "thread_id": str(uuid.uuid4())
            }
        }

        # Mock task to be deleted
        task_to_delete = {
            "id": task_id,
            "tenant_id": tenant_id,
            "title": "Task to Delete",
            "description": "This task will be deleted",
            "status": "todo",
            "created_by": user_id,
            "created_at": datetime.now().isoformat()
        }

        with patch("backend.agents.interactive.task_crud.nodes.delete.get_tool_executor") as mock_get_executor:
            mock_executor = AsyncMock()
            # Mock both get_task and delete_task calls
            def mock_execute_tool(tool_name, tool_args, tenant_id):
                if tool_name == "get_task":
                    return task_to_delete
                elif tool_name == "delete_task":
                    return True
                return None

            mock_executor.execute_tool.side_effect = mock_execute_tool
            mock_get_executor.return_value = mock_executor

            # Execute the graph
            result = await task_crud_graph.ainvoke(state, config)

            # Verify successful deletion
            assert any(
                "deleted" in msg.get("content", "").lower() or
                "removed" in msg.get("content", "").lower()
                for msg in result.get("messages", [])
                if msg.get("role") == "assistant"
            )

            # Verify both tools were called
            assert mock_executor.execute_tool.call_count == 2
            # First call should be get_task
            first_call = mock_executor.execute_tool.call_args_list[0]
            assert first_call[1]["tool_name"] == "get_task"
            assert first_call[1]["tool_args"]["task_id"] == task_id
            assert first_call[1]["tenant_id"] == tenant_id
            # Second call should be delete_task
            second_call = mock_executor.execute_tool.call_args_list[1]
            assert second_call[1]["tool_name"] == "delete_task"
            assert second_call[1]["tool_args"]["task_id"] == task_id
            assert second_call[1]["tenant_id"] == tenant_id

    @pytest.mark.parametrize("tenant_a_id,tenant_b_id", [
        (str(uuid.uuid4()), str(uuid.uuid4())),
        (str(uuid.uuid4()), str(uuid.uuid4())),
    ])
    async def test_tenant_isolation(
        self,
        task_crud_graph,
        mock_voyage,
        mock_task_database,
        tenant_a_id,
        tenant_b_id
    ):
        """Test that tasks from tenant A are not visible to tenant B."""
        user_a_id = str(uuid.uuid4())
        user_b_id = str(uuid.uuid4())

        # Create a task for tenant A
        task_a = {
            "id": str(uuid.uuid4()),
            "tenant_id": tenant_a_id,
            "title": "Tenant A Task",
            "description": "This task belongs to tenant A",
            "status": "todo",
            "created_by": user_a_id,
            "created_at": datetime.now().isoformat()
        }

        # Create a task for tenant B
        task_b = {
            "id": str(uuid.uuid4()),
            "tenant_id": tenant_b_id,
            "title": "Tenant B Task",
            "description": "This task belongs to tenant B",
            "status": "todo",
            "created_by": user_b_id,
            "created_at": datetime.now().isoformat()
        }

        # Test tenant A can only see their tasks
        state_a = {
            "messages": [
                {"role": "user", "content": "List all tasks"}
            ],
            "tenant_id": tenant_a_id,
            "user_id": user_a_id,
            "thread_id": str(uuid.uuid4()),
        }

        config_a = {
            "configurable": {
                "tenant_id": tenant_a_id,
                "user_id": user_a_id,
                "thread_id": str(uuid.uuid4())
            }
        }

        with patch("backend.agents.interactive.task_crud.nodes.read.get_tool_executor") as mock_get_executor:
            mock_executor = AsyncMock()
            # Return only tenant A's tasks
            mock_executor.execute_tool.return_value = [task_a]
            mock_get_executor.return_value = mock_executor

            # Execute the graph for tenant A
            result_a = await task_crud_graph.ainvoke(state_a, config_a)

            # Verify tenant A only sees their tasks
            assert "tasks" in result_a
            assert len(result_a["tasks"]) == 1
            assert result_a["tasks"][0]["tenant_id"] == tenant_a_id
            assert result_a["tasks"][0]["title"] == "Tenant A Task"

            # Verify tool was called with tenant A's ID
            call_args = mock_executor.execute_tool.call_args
            assert call_args[1]["tenant_id"] == tenant_a_id

        # Test tenant B can only see their tasks
        state_b = {
            "messages": [
                {"role": "user", "content": "List all tasks"}
            ],
            "tenant_id": tenant_b_id,
            "user_id": user_b_id,
            "thread_id": str(uuid.uuid4()),
        }

        config_b = {
            "configurable": {
                "tenant_id": tenant_b_id,
                "user_id": user_b_id,
                "thread_id": str(uuid.uuid4())
            }
        }

        with patch("backend.agents.interactive.task_crud.nodes.read.get_tool_executor") as mock_get_executor:
            mock_executor = AsyncMock()
            # Return only tenant B's tasks
            mock_executor.execute_tool.return_value = [task_b]
            mock_get_executor.return_value = mock_executor

            # Execute the graph for tenant B
            result_b = await task_crud_graph.ainvoke(state_b, config_b)

            # Verify tenant B only sees their tasks
            assert "tasks" in result_b
            assert len(result_b["tasks"]) == 1
            assert result_b["tasks"][0]["tenant_id"] == tenant_b_id
            assert result_b["tasks"][0]["title"] == "Tenant B Task"

            # Verify tool was called with tenant B's ID
            call_args = mock_executor.execute_tool.call_args
            assert call_args[1]["tenant_id"] == tenant_b_id
