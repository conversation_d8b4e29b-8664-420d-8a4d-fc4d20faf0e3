"""
Task CRUD Agent State

This module provides state extensions for the Task CRUD Agent, including:
- TaskCrudState class for managing task CRUD operations
- State serialization and deserialization
- State validation and normalization
"""

import logging
from typing import Any, Dict, List, Literal, Optional, TypedDict
from uuid import uuid4

from pydantic import BaseModel, Field

from shared.core.state import <PERSON><PERSON><PERSON><PERSON>tate

# Set up logging
logger = logging.getLogger(__name__)

# Operation type for task CRUD
TaskOperation = Literal["create", "read", "update", "delete", "list"]


class TaskLangGraphState(TypedDict, total=False):
    """
    TypedDict representation of the Task CRUD state for LangGraph.

    This is used for efficient runtime state management and is the primary
    state representation used by LangGraph for task operations.

    Attributes:
        messages: List of messages in the conversation
        tenant_id: The tenant ID for multi-tenancy
        user_id: The user ID
        thread_id: The conversation thread ID
        intent: The detected intent (create|read|update|delete)
        operation: The current operation being performed
        task_id: The ID of the task being operated on
        task_data: The task data for create/update operations
        task_filter: The filter criteria for read operations
        tasks: The list of tasks returned by operations
        current_task: The current task being operated on
        parsed_date: The parsed date from natural language input
        error: Any error that occurred during the operation
        next: The next node to execute (for routing)
        result: The final result of the operation
    """
    # Base state fields
    messages: List[Dict[str, Any]]
    tenant_id: str
    user_id: str
    thread_id: str

    # Task CRUD specific fields
    intent: Optional[TaskOperation]
    operation: Optional[TaskOperation]
    task_id: Optional[str]
    task_data: Optional[Dict[str, Any]]
    task_filter: Optional[Dict[str, Any]]
    tasks: List[Dict[str, Any]]
    current_task: Optional[Dict[str, Any]]
    parsed_date: Optional[str]
    error: Optional[str]

    # Routing and result fields
    next: Optional[str]
    result: Optional[Dict[str, Any]]

class TaskCrudState(BaseModel):
    """
    State for the Task CRUD Agent.
    
    This class extends the base AiLexState with task-specific fields.
    
    Attributes:
        operation: The current operation (create, read, update, delete, list)
        task_id: The ID of the task being operated on
        task_data: The task data for create/update operations
        task_filter: The filter for read operations
        tasks: The list of tasks returned by read operations
        current_task: The current task being operated on
        parsed_date: The parsed date from natural language input
        error: Any error that occurred during the operation
    """
    # Base state fields
    messages: List[Dict[str, Any]] = Field(default_factory=list)
    user_context: Dict[str, Any] = Field(default_factory=dict)
    thread_id: str = Field(default_factory=lambda: str(uuid4()))
    tenant_id: Optional[str] = None
    
    # Task CRUD specific fields
    operation: Optional[TaskOperation] = None
    task_id: Optional[str] = None
    task_data: Optional[Dict[str, Any]] = None
    task_filter: Optional[Dict[str, Any]] = None
    tasks: List[Dict[str, Any]] = Field(default_factory=list)
    current_task: Optional[Dict[str, Any]] = None
    parsed_date: Optional[str] = None
    error: Optional[str] = None
    
    # Routing field
    next: Optional[str] = None
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the state to a dictionary.
        
        Returns:
            Dict[str, Any]: The state as a dictionary
        """
        return self.dict(exclude_none=True)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TaskCrudState":
        """
        Create a state from a dictionary.
        
        Args:
            data: The dictionary to create the state from
            
        Returns:
            TaskCrudState: The created state
        """
        return cls(**data)
    
    @classmethod
    def from_ailex_state(cls, state: AiLexState) -> "TaskCrudState":
        """
        Create a TaskCrudState from an AiLexState.
        
        Args:
            state: The AiLexState to create the TaskCrudState from
            
        Returns:
            TaskCrudState: The created state
        """
        # Extract base fields from AiLexState
        base_data = {
            "messages": state.messages,
            "user_context": state.user_context,
            "thread_id": state.thread_id,
            "tenant_id": state.tenant_id,
        }
        
        # Extract task-specific fields from metadata
        task_data = state.metadata.get("task_crud", {})
        
        # Combine and create TaskCrudState
        return cls(**base_data, **task_data)
    
    def to_ailex_state(self, state: AiLexState) -> AiLexState:
        """
        Update an AiLexState with this TaskCrudState.
        
        Args:
            state: The AiLexState to update
            
        Returns:
            AiLexState: The updated state
        """
        # Update base fields
        state.messages = self.messages
        
        # Update metadata with task-specific fields
        state.metadata["task_crud"] = {
            "operation": self.operation,
            "task_id": self.task_id,
            "task_data": self.task_data,
            "task_filter": self.task_filter,
            "tasks": self.tasks,
            "current_task": self.current_task,
            "parsed_date": self.parsed_date,
            "error": self.error,
        }
        
        # Update next field for routing
        if self.next:
            state.next = self.next
        
        return state
    
    def add_message(
        self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add a message to the state.
        
        Args:
            role: The role of the message sender
            content: The message content
            metadata: Optional metadata for the message
        """
        self.messages.append({
            "role": role,
            "content": content,
            "metadata": metadata or {},
        })
    
    def get_last_message(self) -> Optional[Dict[str, Any]]:
        """
        Get the last message in the state.
        
        Returns:
            Optional[Dict[str, Any]]: The last message, or None if there are no messages
        """
        if not self.messages:
            return None
        return self.messages[-1]


__all__ = [
    "TaskLangGraphState",
    "TaskCrudState",
    "TaskOperation"
]
