"""
Task CRUD Agent Package

This package provides the Task CRUD Agent implementation, which is responsible for:
1. Creating new tasks with title, description, due date, priority, and assignee
2. Reading tasks with filtering by status, date range, and assignee
3. Updating task properties including status transitions
4. Deleting tasks with proper validation

The Task CRUD Agent serves as an interactive agent for task management operations,
providing a natural language interface for task-related operations.

Usage:
    from backend.agents.interactive.task_crud import TaskCrudAgent, task_crud_router

    # Create a task crud agent
    agent = TaskCrudAgent()

    # Execute the agent
    result = await agent.invoke(
        {"messages": [HumanMessage(content="Create a new task to review the contract")]},
        {"configurable": {"thread_id": "123", "tenant_id": "456"}}
    )
"""

from backend.agents.interactive.task_crud.agent import TaskCrudAgent
from backend.agents.interactive.task_crud.router import task_crud_router
from backend.agents.interactive.task_crud.graph import create_task_graph
from backend.agents.interactive.task_crud.nodes import (
    create_task,
    read_task,
    update_task,
    delete_task,
    parse_date,
    return_result
)

__all__ = [
    "TaskCrudAgent",
    "task_crud_router",
    "create_task_graph",
    "create_task",
    "read_task",
    "update_task",
    "delete_task",
    "parse_date",
    "return_result"
]
