"""
Task CRUD Agent Router

This module provides the router node for the Task CRUD Agent, which directs
requests to the appropriate task operation based on the user's intent.

Usage:
    from backend.agents.interactive.task_crud.router import task_crud_router

    # Add router node to the StateGraph
    sg.add_node("taskCrudRouter", task_crud_router)
"""

import json
import logging
import os
import re
from pathlib import Path
from typing import Any, Dict, Literal

from langchain_core.runnables import RunnableConfig
from langchain.output_parsers import StructuredOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field


# Import LLM client if available
try:
    from shared.core.llm import get_llm_client
    HAS_LLM = True
except ImportError:
    HAS_LLM = False

# Set up logging
logger = logging.getLogger(__name__)

# Check if we should use LLM for routing
USE_LLM_ROUTER = (
    os.environ.get("TASK_ROUTER_USE_LLM", "").lower() in ("true", "1", "yes")
    and HAS_LLM
)

# Define the router schema
class RouterOutput(BaseModel):
    """Schema for the router output."""
    op: Literal["create", "read", "update", "delete"] = Field(
        description="The CRUD operation to perform"
    )

# Load the router prompt
ROUTER_PROMPT_PATH = (
    Path(__file__).parent.parent.parent.parent.parent
    / "shared" / "core" / "llm" / "router_prompt.md"
)
ROUTER_PROMPT = (
    ROUTER_PROMPT_PATH.read_text() if ROUTER_PROMPT_PATH.exists() else ""
)


async def task_crud_router(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Router node for task CRUD operations.
    Analyzes the user's message and determines the appropriate operation.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Routing task CRUD operation")

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("role") == "user" or message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    # Determine the operation based on the message content
    operation = await _determine_operation(content)

    # Set the operation and intent in the state
    state["operation"] = operation
    state["intent"] = operation

    # Route to the appropriate node based on the operation
    if operation == "create":
        state["next"] = "create_task"
        logger.info("Routing to create_task node")
    elif operation == "read" or operation == "list":
        state["next"] = "read_task"
        logger.info("Routing to read_task node")
    elif operation == "update":
        state["next"] = "update_task"
        logger.info("Routing to update_task node")
    elif operation == "delete":
        state["next"] = "delete_task"
        logger.info("Routing to delete_task node")
    else:
        # Default to create if we can't determine the operation
        state["next"] = "create_task"
        logger.info("Could not determine operation, defaulting to create_task")

    # Add a message indicating the operation
    operation_messages = {
        "create": "I'll help you create a new task.",
        "read": "I'll help you find and view tasks.",
        "list": "I'll list the tasks for you.",
        "update": "I'll help you update an existing task.",
        "delete": "I'll help you delete a task."
    }

    state["messages"].append({
        "role": "assistant",
        "content": operation_messages.get(operation, "I'll help you with your task."),
        "metadata": {"operation": operation}
    })

    return state


async def _determine_operation(content: str) -> str:
    """
    Determine the operation based on the message content.

    Args:
        content: The message content

    Returns:
        The determined operation (create, read, update, delete, list)
    """
    # Try LLM-based routing if enabled
    if USE_LLM_ROUTER:
        try:
            logger.info("Using LLM for routing")
            llm_client = get_llm_client()

            # Note: We parse the JSON response manually below

            # Call the LLM
            response = await llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": ROUTER_PROMPT},
                    {"role": "user", "content": content}
                ],
                temperature=0,
                model="gpt-4o"  # Use a capable model for accurate classification
            )

            # Extract the content from the response
            assistant_message = response["choices"][0]["message"]["content"]

            # Parse the JSON response
            try:
                # Try to parse directly
                json_str = assistant_message.strip()
                if json_str.startswith("```json"):
                    json_str = json_str.split("```json")[1].split("```")[0].strip()
                elif json_str.startswith("```"):
                    json_str = json_str.split("```")[1].split("```")[0].strip()

                result = json.loads(json_str)
                op = result.get("op", "").lower()

                # Validate the operation
                if op in ["create", "read", "update", "delete"]:
                    logger.info(f"LLM determined operation: {op}")
                    # Map 'read' to 'list' if it's a list operation
                    if op == "read" and _is_list_operation(content):
                        return "list"
                    return op
                else:
                    logger.warning(f"LLM returned invalid operation: {op}")
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"Failed to parse LLM response: {e}")

        except Exception as e:
            logger.warning(f"LLM routing failed: {e}")

    # Fall back to regex-based routing
    logger.info("Using regex for routing")
    return _regex_determine_operation(content)


def _is_list_operation(content: str) -> bool:
    """
    Determine if the operation is a list operation.

    Args:
        content: The message content

    Returns:
        True if it's a list operation, False otherwise
    """
    content = content.lower()
    list_patterns = [
        r"list\s+(?:all\s+)?tasks",
        r"show\s+(?:me\s+)?(?:all\s+)?tasks",
        r"get\s+(?:all\s+)?tasks",
        r"view\s+(?:all\s+)?tasks",
        r"what\s+(?:are\s+)?(?:my\s+)?tasks"
    ]
    for pattern in list_patterns:
        if re.search(pattern, content):
            return True
    return False


def _regex_determine_operation(content: str) -> str:
    """
    Determine the operation based on the message content using regex patterns.

    Args:
        content: The message content

    Returns:
        The determined operation (create, read, update, delete, list)
    """
    content = content.lower()

    # Check for create patterns
    create_patterns = [
        r"create\s+(?:a\s+)?(?:new\s+)?task",
        r"add\s+(?:a\s+)?(?:new\s+)?task",
        r"make\s+(?:a\s+)?(?:new\s+)?task",
        r"new\s+task",
        r"schedule\s+(?:a\s+)?task"
    ]
    for pattern in create_patterns:
        if re.search(pattern, content):
            return "create"

    # Check for read patterns
    read_patterns = [
        r"find\s+(?:a\s+)?task",
        r"search\s+(?:for\s+)?(?:a\s+)?task",
        r"get\s+(?:a\s+)?task",
        r"show\s+(?:me\s+)?(?:a\s+)?task",
        r"view\s+(?:a\s+)?task",
        r"retrieve\s+(?:a\s+)?task"
    ]
    for pattern in read_patterns:
        if re.search(pattern, content):
            return "read"

    # Check for list patterns
    list_patterns = [
        r"list\s+(?:all\s+)?tasks",
        r"show\s+(?:me\s+)?(?:all\s+)?tasks",
        r"get\s+(?:all\s+)?tasks",
        r"view\s+(?:all\s+)?tasks",
        r"what\s+(?:are\s+)?(?:my\s+)?tasks"
    ]
    for pattern in list_patterns:
        if re.search(pattern, content):
            return "list"

    # Check for update patterns
    update_patterns = [
        r"update\s+(?:a\s+)?task",
        r"change\s+(?:a\s+)?task",
        r"modify\s+(?:a\s+)?task",
        r"edit\s+(?:a\s+)?task",
        r"mark\s+(?:a\s+)?task\s+(?:as\s+)?(done|complete|finished|in\s+progress)"
    ]
    for pattern in update_patterns:
        if re.search(pattern, content):
            return "update"

    # Check for delete patterns
    delete_patterns = [
        r"delete\s+(?:a\s+)?task",
        r"remove\s+(?:a\s+)?task",
        r"cancel\s+(?:a\s+)?task"
    ]
    for pattern in delete_patterns:
        if re.search(pattern, content):
            return "delete"

    # Default to create if we can't determine the operation
    return "create"
