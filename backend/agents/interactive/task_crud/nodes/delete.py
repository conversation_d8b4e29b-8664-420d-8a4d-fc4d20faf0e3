"""
Delete Task Node

This module provides a node for deleting tasks with proper validation
to prevent accidental deletion.

Usage:
    from backend.agents.interactive.task_crud.nodes.delete import delete_task

    # Add the node to the StateGraph
    sg.add_node("delete_task", delete_task)
"""

import logging
import re
from typing import Dict, Any, Optional
from datetime import datetime

from langchain_core.runnables import RunnableConfig
from shared.core.tools.executor import get_tool_executor

from backend.agents.interactive.task_crud.utils.string_helpers import has_confirmed_deletion

# Set up logging
logger = logging.getLogger(__name__)


async def delete_task(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Delete a task.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing delete_task node")

    # Extract configurable values
    configurable = config.get("configurable", {})
    tenant_id = configurable.get("tenant_id", "unknown")
    user_id = configurable.get("user_id", "unknown")

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("role") == "user" or message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    # Extract task ID from the message
    task_id = _extract_task_id(content)

    # If we don't have a task ID, ask for one
    if not task_id:
        # Check if we have a current task in the state
        if state.get("current_task") and state["current_task"].get("id"):
            task_id = state["current_task"]["id"]
        else:
            # We need to find the task first
            state["messages"].append({
                "role": "assistant",
                "content": "Which task would you like to delete? Please provide the task ID or title.",
                "metadata": {"awaiting": "task_id"}
            })
            return state

    # Get the task to delete
    tool_executor = get_tool_executor()

    try:
        # Get the task
        task = await tool_executor.execute_tool(
            tool_name="get_task",
            tool_args={"task_id": task_id},
            tenant_id=tenant_id
        )

        if not task:
            logger.warning(f"Task not found: {task_id}")
            state["error"] = f"Task not found: {task_id}"
            state["messages"].append({
                "role": "assistant",
                "content": f"I couldn't find a task with ID {task_id}. Please check the ID and try again.",
                "metadata": {"error": "task_not_found"}
            })
            state["next"] = "FINISH"
            return state

        # Store the current task
        state["current_task"] = task

        # Check if the user has confirmed deletion
        if not has_confirmed_deletion(content):
            # Ask for confirmation
            state["messages"].append({
                "role": "assistant",
                "content": f"Are you sure you want to delete the task '{task.get('title')}'? This action cannot be undone. Please confirm by saying 'yes' or 'confirm'.",
                "metadata": {"awaiting": "confirmation", "current_task": task}
            })
            state["next"] = "FINISH"
            return state

        # Execute the delete_task tool
        result = await tool_executor.execute_tool(
            tool_name="delete_task",
            tool_args={"task_id": task_id},
            tenant_id=tenant_id
        )

        if result:
            logger.info(f"Deleted task: {task_id}")
            state["messages"].append({
                "role": "assistant",
                "content": f"I've deleted the task '{task.get('title')}'.",
                "metadata": {"deleted_task": task}
            })
            state["next"] = "FINISH"
        else:
            logger.warning("Failed to delete task")
            state["error"] = "Failed to delete task"
            state["messages"].append({
                "role": "assistant",
                "content": "I couldn't delete the task. Please try again.",
                "metadata": {"error": "task_deletion_failed"}
            })
            state["next"] = "FINISH"
    except Exception as e:
        logger.error(f"Error deleting task: {str(e)}")
        state["error"] = str(e)
        state["messages"].append({
            "role": "assistant",
            "content": f"I encountered an error deleting the task: {str(e)}. Please try again.",
            "metadata": {"error": str(e)}
        })
        state["next"] = "FINISH"

    return state


def _extract_task_id(content: str) -> Optional[str]:
    """
    Extract task ID from a message.

    Args:
        content: The message content

    Returns:
        The task ID, or None if not found
    """
    # Extract task ID
    task_id_patterns = [
        r"task\s+(?:id|number)[:\s]+([a-f0-9\-]+)",
        r"id[:\s]+([a-f0-9\-]+)",
        r"delete\s+task\s+([a-f0-9\-]+)"
    ]

    for pattern in task_id_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            return match.group(1).strip()

    return None


def _has_confirmed_deletion(content: str) -> bool:
    """
    Check if the user has confirmed deletion.

    Args:
        content: The message content

    Returns:
        True if the user has confirmed deletion, False otherwise
    """
    # Use our string helper function
    return has_confirmed_deletion(content)
