"""
Task CRUD Agent Nodes

This module provides node implementations for the Task CRUD Agent, including:
- create_task: Node for creating new tasks
- read_task: Node for reading and listing tasks
- update_task: Node for updating existing tasks
- delete_task: Node for deleting tasks
- parse_date: Node for parsing natural language dates
"""

from backend.agents.interactive.task_crud.nodes.create import create_task
from backend.agents.interactive.task_crud.nodes.read import read_task
from backend.agents.interactive.task_crud.nodes.update import update_task
from backend.agents.interactive.task_crud.nodes.delete import delete_task
from backend.agents.interactive.task_crud.nodes.date_parser import parse_date
from backend.agents.interactive.task_crud.nodes.return_result import return_result

__all__ = [
    "create_task",
    "read_task",
    "update_task",
    "delete_task",
    "parse_date",
    "return_result"
]
